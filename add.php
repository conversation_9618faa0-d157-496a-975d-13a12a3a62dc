<?php

include_once("connections/connection.php");
$conn = connection();


if(isset($_POST['submit'])) {
    $fname = $_POST["first_name"];
    $lname = $_POST["last_name"];
    $error = "";

    if (empty($fname) || empty($lname)) {
        $error = "All fields are required";
    } else {
        $stmt = $conn->prepare("SELECT * FROM student_list WHERE fname = ? AND lname = ?");
        $stmt->bind_param("ss", $fname, $lname);
        $stmt->execute();

        $result = $stmt->get_result();
        $user = $result->fetch_assoc();
    
        if($user) {
            $error = "Name Already Exist";
        } else {
            $stmt = $conn->prepare("INSERT INTO student_list (fname, lname) VALUES ( ?, ?)");
            $stmt->bind_param("ss", $fname, $lname);
            $stmt->execute();

            header("location: i
            ndex.php");
            exit(); 
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <form action="" method="POST">
    <label>First Name</label>
    <input type="text" name="first_name"><br>
    
    <label>Last Name</label>
    <input type="text" name="last_name"><br>

    <select name="gender">
        <option value="male">Male</option>
        <option value="male">Female</option>
    </select>

    <input type="submit" name="submit" value="Submit Form">
    </form>
    <p style="color:red;"><?php echo $error; ?></p>
    <a href="index.php">Home page</a>
</body>
</html>
