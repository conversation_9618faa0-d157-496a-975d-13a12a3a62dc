<?php

if(!isset($_SESSION)) {
    session_start();
}

include_once("connections/connection.php");
$conn = connection(); 

$error = "";

if(isset($_POST["login"])) {
    $user = $_POST["user"];
    $password = $_POST["password"];

    if(empty($user) || empty($password)) {
        $error = "All fields are required";
    } else {

    $sql = ("SELECT * FROM student_users WHERE username='$user' AND password='$password'");
    $user = $conn->query($sql) or die ($conn->error);
    $row = $user->fetch_assoc();
    $total = $user->num_rows;

    if($total > 0) {
        echo $_SESSION['UserLogin'] = $row['username'];
        $_SESSION['Access'] = $row['access'];
        header("location: index.php");
    } else {
        echo "No user found!";
    }
}
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <h1>Login</h1>
    <br>
    <br>

    <dv style="text-align: center;">
    <form action="" method="POST">
    <label>Username</label>
    <input type="text" name="user"><br>
    
    <label>Password</label>
    <input type="password" name="password"><br>

    <button type="submit" name="login">Login</button>
    </form>
    </dv>
    <p style="color:red;"><?php echo $error;?></p>
    <a href="index.php">Home page</a>
</body>
</html>