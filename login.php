<?php

include_once("connections/connection.php");
$conn = connection(); 

if(isset($_POST["login"])) {
    $user = $_POST["user"];
    $password = $_POST["password"];

    $sql = ("SELECT * FROM student_users WHERE username='$user' AND password='$password'");
    $user = $conn->query($sql) or die ($conn->error);
    $row = $user->fetch_assoc();
    $total = $user->num_rows;

    if($total > 0) {
        echo $_SESSION['UserLogin'] = $row['username'];
        echo $_SESSION['Access'] = $row['access'];
        //header("location: index.php");
    }
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <h1>Login</h1>
    <br>
    <br>

    <dv style="text-align: center;">
    <form action="" method="POST">
    <label>Username</label>
    <input type="text" name="user"><br>
    
    <label>Password</label>
    <input type="password" name="password"><br>

    <button type="submit" name="login">Login</button>
    </form>
    </dv>
    <a href="index.php">Home page</a>
</body>
</html>