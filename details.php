<?php

if(!isset($_SESSION)) {
    session_start();
}

if(isset($_SESSION['Access']) && $_SESSION['Access'] == "administrator") {
    echo "Welcome! ".$_SESSION['UserLogin'];
}
else {
    header("location: index.php");
}

include_once("connections/connection.php");

$conn = connection();

// if(isset($_GET['id'])) {
//     $id = $_GET['id'];

//     $sql = "SELECT * FROM student_list WHERE id = '$id'";
//     $student = $conn->query($sql) or die($conn->error);
//     $row = $student->fetch_assoc();
// } else {
//     $row = null;
// }

    $id = $_GET['id'];

    $sql = "SELECT * FROM student_list WHERE id = '$id'";
    $student = $conn->query($sql) or die($conn->error);
    $row = $student->fetch_assoc();

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>

    <h2><?php echo $row['fname'];?> <?php echo $row['lname'];?></h2>

 



<?php /*
<?php if($row) { ?>
    <h2><?php echo $row['fname'];?> <?php echo $row['lname']; ?></h2>
<?php } else { ?>
    <p>Student not found</p>
<?php } ?>

*/?>

</body>
</html>
