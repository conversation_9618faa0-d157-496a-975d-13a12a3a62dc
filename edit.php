<?php

include_once("connections/connection.php");
$conn = connection();

$id = $_GET['id'];

    $sql = "SELECT * FROM student_list WHERE id = '$id'";
    $student = $conn->query($sql) or die($conn->error);
    $row = $student->fetch_assoc();


if(isset($_POST['submit'])) {

    $fname = $_POST["first_name"];
    $lname = $_POST["last_name"];
    $gender = $_POST["gender"];

     $stmt = $conn->prepare("UPDATE FROM student_list SET fname = '$fname',  lname = '$lname', gender = '$gender' WHERE id = '$id'");
     $stmt->bind_param("sss", $fname, $lname, $gender);
     $stmt->execute();

    header("location: details.php");
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <form action="" method="POST">
    <label>First Name</label>
    <input type="text" name="first_name" value="<?php echo $row['fname']?>"><br>
    
    <label>Last Name</label>
    <input type="text" name="last_name" value="<?php echo $row['lname']?>"><br>

    <select name="gender">
        <option value="male">Male</option>
        <option value="female">Female</option>
    </select>

    <input type="submit" name="submit" value="Submit Form">
    </form>
    <a href="index.php">Home page</a>
</body>
</html>
