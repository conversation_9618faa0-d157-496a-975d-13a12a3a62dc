<?php

include_once("connections/connection.php");
$conn = connection();

$id = $_GET['id'];

    $sql = "SELECT * FROM student_list WHERE id = '$id'";
    $student = $conn->query($sql) or die($conn->error);
    $row = $student->fetch_assoc();


if(isset($_POST['submit'])) {

    $fname = $_POST["first_name"];
    $lname = $_POST["last_name"];
    $gender = $_POST["gender"];

     $stmt = $conn->prepare("UPDATE student_list SET fname = ?, lname = ?, gender = ? WHERE id = ?");
     $stmt->bind_param("sssi", $fname, $lname, $gender, $id);
     $stmt->execute();

    //  $stmt = $conn->prepare("UPDATE FROM student_list SET fname = '$fname',  lname = '$lname', gender = '$gender' WHERE id = '$id'");
    //  $stmt->bind_param("sss", $fname, $lname, $gender);
    //  $stmt->execute();

    header("location: details.php?id=".$_GET['id']);
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <form action="" method="POST">
    <label>First Name</label>
    <input type="text" name="first_name" value="<?php echo $row['fname']?>"><br>
    
    <label>Last Name</label>
    <input type="text" name="last_name" value="<?php echo $row['lname']?>"><br>

    <select name="gender">
        <option value="male" <?php echo ($row['gender'] == "male") ? 'selected' : '' ?>>Male</option>
        <option value="female" <?php echo ($row['gender'] == "female") ? 'selected' : '' ?>>Female</option>
        <option value="unkown" <?php echo ($row['gender'] == "unknown") ? 'selected' : '' ?>>Unknown</option>
    </select>

    <input type="submit" name="submit" value="Update">
    </form>
    <a href="index.php">Home page</a>
    </select>
</body>
</html>
