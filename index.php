<?php

include_once("connections/connection.php");

$conn = connection();

$sql = "SELECT * FROM student_list";
$student = $conn->query($sql) or die($conn->error);
$row = $student->fetch_assoc();

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <h1>Student Management System</h1>
    <br/>
    <br/>
    <a href="add.php">Add Student</a>
    <table>
        <thead>
            <tr>
                <th>First Name</th>
                <th>Last Name</th>
            </tr>
        </thead>
        <tbody>

        <?php do { ?>
        <tr>
            <td><?php echo $row['fname'];?></td>
            <td><?php echo $row['lname'];?></td>
        </tr>
        <?php } while($row = $student->fetch_assoc()) ?>
        </tbody>
    </table>
</body>
</html>
