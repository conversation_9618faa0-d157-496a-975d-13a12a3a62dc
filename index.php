<?php

if(!isset($_SESSION)) {
    session_start();
}

if(isset($_GET['action']) && $_GET['action'] == 'view') {
    if(!isset($_SESSION['UserLogin'])) {
        $message = "You are not allow to view. Please log in first.";
    } else if(!isset($_SESSION['Access']) || $_SESSION['Access'] != "administrator") {
        $message = "You are not allow to view";
    } else {
        header("location: details.php");
    }
}

if(isset($_SESSION['UserLogin'])) {
    echo "Welcome! ".$_SESSION['UserLogin'];
}
else {
    echo "Welcome Guest!";
}

include_once("connections/connection.php");

$conn = connection(); 

$sql = "SELECT * FROM student_list ORDER BY id DESC";
$student = $conn->query($sql) or die($conn->error);
$row = $student->fetch_assoc();

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Management System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <h1>Student Management System</h1>
    <br/>
    <br/>

    <?php if(isset($message)) { ?>
    <p style="color:red;"><?php echo $message; ?></p>
    <?php } ?>

    <?php if(isset($_SESSION['UserLogin'])) {?>
        <a href="logout.php">Log out</a>
        
    <?php } else { ?>
        <a href="login.php">Log in</a>
    <?php } ?>

    <a href="add.php">Add Student</a>
    <table>
        <thead>
            <tr>
                <th>View</th>
                <th>First Name</th>
                <th>Last Name</th>
            </tr>
        </thead>
        <tbody>

        <?php do { ?>
        <tr>
            <td><a href="details.php?action=view&id=<?php echo $row['id']; ?>">View</a></td>
            <td><?php echo $row['fname'];?></td>
            <td><?php echo $row['lname'];?></td>
        </tr>
        <?php } while($row = $student->fetch_assoc()) ?>
        </tbody>
    </table>
</body>
</html>
